<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅转换 - VLESS to Clash</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.8;
            font-size: 1.1em;
        }
        
        .content {
            padding: 40px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        input[type="url"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input[type="url"]:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        textarea {
            height: 120px;
            resize: vertical;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
            margin-right: 10px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .result {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            display: none;
        }
        
        .result.success {
            border-left: 4px solid #27ae60;
        }
        
        .result.error {
            border-left: 4px solid #e74c3c;
            background: #fdf2f2;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .config-preview {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 订阅转换</h1>
            <p>将 VLESS 订阅转换为 Clash 配置</p>
        </div>
        
        <div class="content">
            <form id="convertForm">
                <div class="form-group">
                    <label for="subscriptionUrl">订阅链接或 VLESS 链接:</label>
                    <input type="url" id="subscriptionUrl" placeholder="vless://... 或 https://..." required>
                </div>
                
                <div class="form-group">
                    <label for="customRules">自定义规则 (可选):</label>
                    <textarea id="customRules" placeholder="每行一个规则，例如：&#10;DOMAIN-SUFFIX,google.com,PROXY&#10;DOMAIN-KEYWORD,youtube,PROXY"></textarea>
                </div>
                
                <button type="submit" class="btn" id="convertBtn">🔄 开始转换</button>
            </form>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在转换配置...</p>
            </div>
            
            <div class="result" id="result">
                <div id="resultContent"></div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('convertForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const url = document.getElementById('subscriptionUrl').value.trim();
            const customRules = document.getElementById('customRules').value.trim();
            const convertBtn = document.getElementById('convertBtn');
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            
            if (!url) {
                alert('请输入订阅链接');
                return;
            }
            
            // 显示加载状态
            convertBtn.disabled = true;
            loading.style.display = 'block';
            result.style.display = 'none';
            
            try {
                const response = await fetch('/api/convert', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: url,
                        config: {
                            custom_rules: customRules.split('\n').filter(rule => rule.trim())
                        }
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showResult(true, `转换成功！共解析到 ${data.proxy_count} 个代理节点`, data.config);
                } else {
                    showResult(false, data.error || '转换失败');
                }
                
            } catch (error) {
                showResult(false, '网络错误: ' + error.message);
            } finally {
                convertBtn.disabled = false;
                loading.style.display = 'none';
            }
        });
        
        function showResult(success, message, config = null) {
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            result.className = 'result ' + (success ? 'success' : 'error');
            
            let html = `<p><strong>${message}</strong></p>`;
            
            if (success && config) {
                html += `
                    <button class="btn btn-success" onclick="downloadConfig()">📥 下载配置文件</button>
                    <div class="config-preview">${JSON.stringify(config, null, 2)}</div>
                `;
                window.currentConfig = config;
            }
            
            resultContent.innerHTML = html;
            result.style.display = 'block';
        }
        
        async function downloadConfig() {
            if (!window.currentConfig) return;
            
            try {
                const response = await fetch('/api/download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        config: window.currentConfig
                    })
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = response.headers.get('Content-Disposition').split('filename=')[1];
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                } else {
                    alert('下载失败');
                }
            } catch (error) {
                alert('下载错误: ' + error.message);
            }
        }
    </script>
</body>
</html>
