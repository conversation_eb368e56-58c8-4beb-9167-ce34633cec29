# VLESS 订阅转换服务

一个基于 Python Flask 的订阅转换服务，支持将 VLESS 协议链接转换为 Clash 配置文件。

## 功能特性

- ✅ 支持 VLESS 协议解析
- ✅ 支持多种传输协议 (TCP, WebSocket, gRPC, HTTP/2)
- ✅ 支持 TLS 和 Reality 安全传输
- ✅ 自动生成 Clash 配置
- ✅ 内置常用规则集
- ✅ 简洁的 Web 界面
- ✅ API 接口支持

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行服务

```bash
python app.py
```

服务将在 `http://localhost:5000` 启动。

### 3. 使用方法

#### Web 界面
1. 打开浏览器访问 `http://localhost:5000`
2. 输入 VLESS 订阅链接或单个 VLESS 链接
3. 可选：添加自定义规则
4. 点击"开始转换"
5. 下载生成的 Clash 配置文件

#### API 接口

**转换订阅**
```bash
curl -X POST http://localhost:5000/api/convert \
  -H "Content-Type: application/json" \
  -d '{
    "url": "vless://uuid@server:port?params#name",
    "config": {
      "custom_rules": ["DOMAIN-SUFFIX,example.com,PROXY"]
    }
  }'
```

**下载配置**
```bash
curl -X POST http://localhost:5000/api/download \
  -H "Content-Type: application/json" \
  -d '{"config": {...}}' \
  -o clash_config.yaml
```

## 支持的协议参数

### VLESS 链接格式
```
vless://uuid@server:port?type=tcp&security=tls&sni=example.com#节点名称
```

### 支持的参数
- `type`: 传输协议 (tcp, ws, grpc, h2, kcp, quic)
- `security`: 安全传输 (none, tls, reality)
- `sni`: TLS 服务器名称
- `alpn`: ALPN 协议
- `path`: WebSocket/HTTP2 路径
- `host`: 请求头 Host
- `serviceName`: gRPC 服务名
- `flow`: 流控制
- `pbk`: Reality 公钥
- `sid`: Reality 短 ID

## 部署到服务器

### 使用 Gunicorn

```bash
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 使用 Docker

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

### 使用 Nginx 反向代理

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 项目结构

```
ui-toclash/
├── app.py                 # 主应用程序
├── requirements.txt       # Python 依赖
├── README.md             # 项目说明
├── parsers/              # 解析器模块
│   ├── __init__.py
│   └── vless_parser.py   # VLESS 链接解析器
├── generators/           # 生成器模块
│   ├── __init__.py
│   └── clash_generator.py # Clash 配置生成器
├── config/               # 配置模块
│   ├── __init__.py
│   └── templates.py      # 配置模板
└── templates/            # Web 模板
    └── index.html        # 主页面
```

## 注意事项

1. 请确保输入的 VLESS 链接格式正确
2. 生成的配置文件适用于 Clash Premium 内核
3. 规则集会自动从 CDN 更新，首次使用需要网络连接
4. 建议在生产环境中使用 HTTPS

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
