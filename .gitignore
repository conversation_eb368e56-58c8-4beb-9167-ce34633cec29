# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Logs
logs/
*.log

# SSL certificates
ssl/
*.pem
*.key
*.crt

# Config files with sensitive data
config.ini
.env

# OS
.DS_Store
Thumbs.db

# Docker
.dockerignore
