#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Clash 配置生成器
将解析的代理信息转换为 Clash YAML 配置格式
"""

import yaml
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class ClashGenerator:
    """Clash 配置生成器"""
    
    def __init__(self):
        self.default_rules = [
            "RULE-SET,reject,REJECT",
            "RULE-SET,icloud,DIRECT",
            "RULE-SET,apple,DIRECT",
            "RULE-SET,google,PROXY",
            "RULE-SET,proxy,PROXY",
            "RULE-SET,direct,DIRECT",
            "RULE-SET,private,DIRECT",
            "RULE-SET,gfw,PROXY",
            "RULE-SET,tld-not-cn,PROXY",
            "RULE-SET,telegramcidr,PROXY",
            "RULE-SET,cncidr,DIRECT",
            "RULE-SET,lancidr,DIRECT",
            "RULE-SET,applications,DIRECT",
            "GEOIP,LAN,DIRECT",
            "GEOIP,CN,DIRECT",
            "MATCH,PROXY"
        ]
        
        self.rule_providers = {
            "reject": {
                "type": "http",
                "behavior": "domain",
                "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/reject.txt",
                "path": "./ruleset/reject.yaml",
                "interval": 86400
            },
            "icloud": {
                "type": "http",
                "behavior": "domain",
                "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/icloud.txt",
                "path": "./ruleset/icloud.yaml",
                "interval": 86400
            },
            "apple": {
                "type": "http",
                "behavior": "domain",
                "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/apple.txt",
                "path": "./ruleset/apple.yaml",
                "interval": 86400
            },
            "google": {
                "type": "http",
                "behavior": "domain",
                "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/google.txt",
                "path": "./ruleset/google.yaml",
                "interval": 86400
            },
            "proxy": {
                "type": "http",
                "behavior": "domain",
                "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/proxy.txt",
                "path": "./ruleset/proxy.yaml",
                "interval": 86400
            },
            "direct": {
                "type": "http",
                "behavior": "domain",
                "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/direct.txt",
                "path": "./ruleset/direct.yaml",
                "interval": 86400
            },
            "private": {
                "type": "http",
                "behavior": "domain",
                "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/private.txt",
                "path": "./ruleset/private.yaml",
                "interval": 86400
            },
            "gfw": {
                "type": "http",
                "behavior": "domain",
                "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/gfw.txt",
                "path": "./ruleset/gfw.yaml",
                "interval": 86400
            },
            "tld-not-cn": {
                "type": "http",
                "behavior": "domain",
                "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/tld-not-cn.txt",
                "path": "./ruleset/tld-not-cn.yaml",
                "interval": 86400
            },
            "telegramcidr": {
                "type": "http",
                "behavior": "ipcidr",
                "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/telegramcidr.txt",
                "path": "./ruleset/telegramcidr.yaml",
                "interval": 86400
            },
            "cncidr": {
                "type": "http",
                "behavior": "ipcidr",
                "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/cncidr.txt",
                "path": "./ruleset/cncidr.yaml",
                "interval": 86400
            },
            "lancidr": {
                "type": "http",
                "behavior": "ipcidr",
                "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/lancidr.txt",
                "path": "./ruleset/lancidr.yaml",
                "interval": 86400
            },
            "applications": {
                "type": "http",
                "behavior": "classical",
                "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/applications.txt",
                "path": "./ruleset/applications.yaml",
                "interval": 86400
            }
        }
    
    def generate(self, proxies, custom_config=None):
        """生成 Clash 配置"""
        if not proxies:
            raise ValueError("代理列表不能为空")
        
        # 基础配置
        config = {
            "port": 7890,
            "socks-port": 7891,
            "redir-port": 7892,
            "mixed-port": 7893,
            "allow-lan": False,
            "bind-address": "*",
            "mode": "rule",
            "log-level": "info",
            "external-controller": "127.0.0.1:9090",
            "dns": {
                "enable": True,
                "ipv6": False,
                "default-nameserver": [
                    "*********",
                    "************"
                ],
                "enhanced-mode": "fake-ip",
                "fake-ip-range": "**********/16",
                "use-hosts": True,
                "nameserver": [
                    "https://doh.pub/dns-query",
                    "https://dns.alidns.com/dns-query"
                ],
                "fallback": [
                    "https://doh.dns.sb/dns-query",
                    "https://dns.cloudflare.com/dns-query",
                    "https://dns.twnic.tw/dns-query",
                    "tls://*******:853"
                ],
                "fallback-filter": {
                    "geoip": True,
                    "geoip-code": "CN",
                    "ipcidr": [
                        "240.0.0.0/4"
                    ]
                }
            }
        }
        
        # 转换代理配置
        clash_proxies = []
        proxy_names = []
        
        for proxy in proxies:
            clash_proxy = self._convert_proxy(proxy)
            if clash_proxy:
                clash_proxies.append(clash_proxy)
                proxy_names.append(clash_proxy['name'])
        
        if not clash_proxies:
            raise ValueError("没有有效的代理配置")
        
        config['proxies'] = clash_proxies
        
        # 代理组配置
        config['proxy-groups'] = self._generate_proxy_groups(proxy_names)
        
        # 规则提供者
        config['rule-providers'] = self.rule_providers
        
        # 规则配置
        rules = self.default_rules.copy()
        
        # 添加自定义规则
        if custom_config and 'custom_rules' in custom_config:
            custom_rules = custom_config['custom_rules']
            if isinstance(custom_rules, list):
                rules = custom_rules + rules
        
        config['rules'] = rules
        
        return config
    
    def _convert_proxy(self, proxy):
        """转换单个代理配置为 Clash 格式"""
        try:
            if proxy['type'] != 'vless':
                logger.warning(f"不支持的代理类型: {proxy['type']}")
                return None
            
            clash_proxy = {
                'name': proxy['name'],
                'type': 'vless',
                'server': proxy['server'],
                'port': proxy['port'],
                'uuid': proxy['uuid'],
                'cipher': 'none'
            }
            
            # 传输协议
            network = proxy.get('network', 'tcp')
            if network != 'tcp':
                clash_proxy['network'] = network
            
            # TLS 配置
            if proxy.get('tls'):
                clash_proxy['tls'] = True
                
                if 'servername' in proxy:
                    clash_proxy['servername'] = proxy['servername']
                
                if 'alpn' in proxy:
                    clash_proxy['alpn'] = proxy['alpn']
                
                if proxy.get('skip-cert-verify'):
                    clash_proxy['skip-cert-verify'] = True
            
            # Reality 配置
            if 'reality-opts' in proxy:
                clash_proxy['reality-opts'] = proxy['reality-opts']
            
            # WebSocket 配置
            if 'ws-opts' in proxy:
                clash_proxy['ws-opts'] = proxy['ws-opts']
            
            # gRPC 配置
            if 'grpc-opts' in proxy:
                clash_proxy['grpc-opts'] = proxy['grpc-opts']
            
            # HTTP/2 配置
            if 'h2-opts' in proxy:
                clash_proxy['h2-opts'] = proxy['h2-opts']
            
            # 流控制
            if 'flow' in proxy:
                clash_proxy['flow'] = proxy['flow']
            
            return clash_proxy
            
        except Exception as e:
            logger.error(f"转换代理配置失败: {e}")
            return None
    
    def _generate_proxy_groups(self, proxy_names):
        """生成代理组配置"""
        return [
            {
                "name": "PROXY",
                "type": "select",
                "proxies": ["♻️ 自动选择", "🚀 手动切换", "DIRECT"] + proxy_names
            },
            {
                "name": "♻️ 自动选择",
                "type": "url-test",
                "proxies": proxy_names,
                "url": "http://www.gstatic.com/generate_204",
                "interval": 300,
                "tolerance": 50
            },
            {
                "name": "🚀 手动切换",
                "type": "select",
                "proxies": proxy_names
            },
            {
                "name": "🌍 国外媒体",
                "type": "select",
                "proxies": ["PROXY", "♻️ 自动选择", "🚀 手动切换", "DIRECT"] + proxy_names
            },
            {
                "name": "📺 哔哩哔哩",
                "type": "select",
                "proxies": ["DIRECT", "PROXY", "♻️ 自动选择", "🚀 手动切换"] + proxy_names
            },
            {
                "name": "🍎 苹果服务",
                "type": "select",
                "proxies": ["DIRECT", "PROXY", "♻️ 自动选择", "🚀 手动切换"] + proxy_names
            },
            {
                "name": "🎯 全球直连",
                "type": "select",
                "proxies": ["DIRECT", "PROXY", "♻️ 自动选择", "🚀 手动切换"] + proxy_names
            },
            {
                "name": "🛑 广告拦截",
                "type": "select",
                "proxies": ["REJECT", "DIRECT"]
            },
            {
                "name": "🐟 漏网之鱼",
                "type": "select",
                "proxies": ["PROXY", "DIRECT", "♻️ 自动选择", "🚀 手动切换"] + proxy_names
            }
        ]
