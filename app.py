#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订阅转换服务主程序
支持 VLESS 等协议转换为 Clash 配置
"""

from flask import Flask, render_template, request, jsonify, Response
import yaml
import base64
import urllib.parse
import json
import re
from datetime import datetime
import logging

from parsers.vless_parser import VlessParser
from generators.clash_generator import ClashGenerator

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/convert', methods=['POST'])
def convert_subscription():
    """转换订阅链接"""
    try:
        data = request.get_json()
        subscription_url = data.get('url', '').strip()
        custom_config = data.get('config', {})
        
        if not subscription_url:
            return jsonify({'error': '请提供订阅链接'}), 400
        
        # 解析订阅内容
        proxies = []
        
        # 如果是单个 VLESS 链接
        if subscription_url.startswith('vless://'):
            parser = VlessParser()
            proxy = parser.parse(subscription_url)
            if proxy:
                proxies.append(proxy)
        else:
            # 处理订阅链接
            import requests
            try:
                response = requests.get(subscription_url, timeout=10)
                response.raise_for_status()
                
                # 尝试解码 base64
                try:
                    content = base64.b64decode(response.text).decode('utf-8')
                except:
                    content = response.text
                
                # 解析每一行
                parser = VlessParser()
                for line in content.strip().split('\n'):
                    line = line.strip()
                    if line.startswith('vless://'):
                        proxy = parser.parse(line)
                        if proxy:
                            proxies.append(proxy)
                            
            except Exception as e:
                logger.error(f"获取订阅内容失败: {e}")
                return jsonify({'error': f'获取订阅内容失败: {str(e)}'}), 400
        
        if not proxies:
            return jsonify({'error': '未找到有效的代理配置'}), 400
        
        # 生成 Clash 配置
        generator = ClashGenerator()
        clash_config = generator.generate(proxies, custom_config)
        
        return jsonify({
            'success': True,
            'config': clash_config,
            'proxy_count': len(proxies)
        })
        
    except Exception as e:
        logger.error(f"转换失败: {e}")
        return jsonify({'error': f'转换失败: {str(e)}'}), 500

@app.route('/api/download', methods=['POST'])
def download_config():
    """下载配置文件"""
    try:
        data = request.get_json()
        config = data.get('config', {})
        
        if not config:
            return jsonify({'error': '配置为空'}), 400
        
        yaml_content = yaml.dump(config, default_flow_style=False, allow_unicode=True)
        
        return Response(
            yaml_content,
            mimetype='application/x-yaml',
            headers={
                'Content-Disposition': f'attachment; filename=clash_config_{datetime.now().strftime("%Y%m%d_%H%M%S")}.yaml'
            }
        )
        
    except Exception as e:
        logger.error(f"下载失败: {e}")
        return jsonify({'error': f'下载失败: {str(e)}'}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
