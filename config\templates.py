#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Clash 配置模板
提供不同场景的配置模板
"""

# 基础模板
BASIC_TEMPLATE = {
    "port": 7890,
    "socks-port": 7891,
    "redir-port": 7892,
    "mixed-port": 7893,
    "allow-lan": False,
    "bind-address": "*",
    "mode": "rule",
    "log-level": "info",
    "external-controller": "127.0.0.1:9090",
    "dns": {
        "enable": True,
        "ipv6": False,
        "default-nameserver": [
            "*********",
            "************"
        ],
        "enhanced-mode": "fake-ip",
        "fake-ip-range": "**********/16",
        "use-hosts": True,
        "nameserver": [
            "https://doh.pub/dns-query",
            "https://dns.alidns.com/dns-query"
        ],
        "fallback": [
            "https://doh.dns.sb/dns-query",
            "https://dns.cloudflare.com/dns-query"
        ],
        "fallback-filter": {
            "geoip": True,
            "geoip-code": "CN",
            "ipcidr": ["240.0.0.0/4"]
        }
    }
}

# 游戏优化模板
GAMING_TEMPLATE = {
    **BASIC_TEMPLATE,
    "port": 7890,
    "socks-port": 7891,
    "redir-port": 7892,
    "mixed-port": 7893,
    "allow-lan": True,
    "mode": "rule",
    "log-level": "warning",
    "dns": {
        **BASIC_TEMPLATE["dns"],
        "enhanced-mode": "redir-host",
        "nameserver": [
            "***************",
            "*********"
        ]
    }
}

# 隐私保护模板
PRIVACY_TEMPLATE = {
    **BASIC_TEMPLATE,
    "mode": "rule",
    "log-level": "silent",
    "dns": {
        **BASIC_TEMPLATE["dns"],
        "nameserver": [
            "https://dns.cloudflare.com/dns-query",
            "https://dns.quad9.net/dns-query"
        ],
        "fallback": [
            "tls://*******:853",
            "tls://*******:853"
        ]
    }
}

# 常用规则集
COMMON_RULES = [
    # 广告拦截
    "RULE-SET,reject,REJECT",
    
    # 直连规则
    "RULE-SET,icloud,DIRECT",
    "RULE-SET,apple,DIRECT", 
    "RULE-SET,private,DIRECT",
    "RULE-SET,direct,DIRECT",
    
    # 代理规则
    "RULE-SET,google,PROXY",
    "RULE-SET,proxy,PROXY",
    "RULE-SET,gfw,PROXY",
    "RULE-SET,tld-not-cn,PROXY",
    "RULE-SET,telegramcidr,PROXY",
    
    # 地理位置规则
    "RULE-SET,cncidr,DIRECT",
    "RULE-SET,lancidr,DIRECT",
    "GEOIP,LAN,DIRECT",
    "GEOIP,CN,DIRECT",
    
    # 兜底规则
    "MATCH,PROXY"
]

# 游戏规则
GAMING_RULES = [
    # 游戏平台直连
    "DOMAIN-SUFFIX,steam-chat.com,DIRECT",
    "DOMAIN-SUFFIX,steamcontent.com,DIRECT",
    "DOMAIN-SUFFIX,steamusercontent.com,DIRECT",
    "DOMAIN-SUFFIX,steamstatic.com,DIRECT",
    
    # 国内游戏直连
    "DOMAIN-SUFFIX,qq.com,DIRECT",
    "DOMAIN-SUFFIX,tencent.com,DIRECT",
    "DOMAIN-SUFFIX,netease.com,DIRECT",
    "DOMAIN-SUFFIX,mihoyo.com,DIRECT",
    
    # 其他规则
    *COMMON_RULES
]

# 媒体规则
MEDIA_RULES = [
    # 国外媒体
    "DOMAIN-SUFFIX,netflix.com,🌍 国外媒体",
    "DOMAIN-SUFFIX,youtube.com,🌍 国外媒体",
    "DOMAIN-SUFFIX,googlevideo.com,🌍 国外媒体",
    "DOMAIN-SUFFIX,twitter.com,🌍 国外媒体",
    "DOMAIN-SUFFIX,facebook.com,🌍 国外媒体",
    "DOMAIN-SUFFIX,instagram.com,🌍 国外媒体",
    
    # 国内媒体
    "DOMAIN-SUFFIX,bilibili.com,📺 哔哩哔哩",
    "DOMAIN-SUFFIX,hdslb.com,📺 哔哩哔哩",
    "DOMAIN-SUFFIX,acgvideo.com,📺 哔哩哔哩",
    
    # 其他规则
    *COMMON_RULES
]

# 规则提供者配置
RULE_PROVIDERS = {
    "reject": {
        "type": "http",
        "behavior": "domain",
        "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/reject.txt",
        "path": "./ruleset/reject.yaml",
        "interval": 86400
    },
    "icloud": {
        "type": "http", 
        "behavior": "domain",
        "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/icloud.txt",
        "path": "./ruleset/icloud.yaml",
        "interval": 86400
    },
    "apple": {
        "type": "http",
        "behavior": "domain", 
        "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/apple.txt",
        "path": "./ruleset/apple.yaml",
        "interval": 86400
    },
    "google": {
        "type": "http",
        "behavior": "domain",
        "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/google.txt", 
        "path": "./ruleset/google.yaml",
        "interval": 86400
    },
    "proxy": {
        "type": "http",
        "behavior": "domain",
        "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/proxy.txt",
        "path": "./ruleset/proxy.yaml", 
        "interval": 86400
    },
    "direct": {
        "type": "http",
        "behavior": "domain",
        "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/direct.txt",
        "path": "./ruleset/direct.yaml",
        "interval": 86400
    },
    "private": {
        "type": "http",
        "behavior": "domain",
        "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/private.txt",
        "path": "./ruleset/private.yaml",
        "interval": 86400
    },
    "gfw": {
        "type": "http", 
        "behavior": "domain",
        "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/gfw.txt",
        "path": "./ruleset/gfw.yaml",
        "interval": 86400
    },
    "tld-not-cn": {
        "type": "http",
        "behavior": "domain",
        "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/tld-not-cn.txt",
        "path": "./ruleset/tld-not-cn.yaml",
        "interval": 86400
    },
    "telegramcidr": {
        "type": "http",
        "behavior": "ipcidr", 
        "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/telegramcidr.txt",
        "path": "./ruleset/telegramcidr.yaml",
        "interval": 86400
    },
    "cncidr": {
        "type": "http",
        "behavior": "ipcidr",
        "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/cncidr.txt", 
        "path": "./ruleset/cncidr.yaml",
        "interval": 86400
    },
    "lancidr": {
        "type": "http",
        "behavior": "ipcidr",
        "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/lancidr.txt",
        "path": "./ruleset/lancidr.yaml",
        "interval": 86400
    },
    "applications": {
        "type": "http",
        "behavior": "classical",
        "url": "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/applications.txt",
        "path": "./ruleset/applications.yaml", 
        "interval": 86400
    }
}

def get_template(template_name="basic"):
    """获取配置模板"""
    templates = {
        "basic": BASIC_TEMPLATE,
        "gaming": GAMING_TEMPLATE, 
        "privacy": PRIVACY_TEMPLATE
    }
    return templates.get(template_name, BASIC_TEMPLATE)

def get_rules(rule_type="common"):
    """获取规则集"""
    rules = {
        "common": COMMON_RULES,
        "gaming": GAMING_RULES,
        "media": MEDIA_RULES
    }
    return rules.get(rule_type, COMMON_RULES)
