#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VLESS 链接解析器
支持解析 VLESS 协议的各种参数
"""

import urllib.parse
import base64
import json
import logging

logger = logging.getLogger(__name__)

class VlessParser:
    """VLESS 链接解析器"""
    
    def __init__(self):
        self.supported_networks = ['tcp', 'ws', 'grpc', 'h2', 'kcp', 'quic']
        self.supported_securities = ['none', 'tls', 'reality']
    
    def parse(self, vless_url):
        """
        解析 VLESS 链接
        格式: vless://uuid@server:port?params#name
        """
        try:
            if not vless_url.startswith('vless://'):
                logger.error("不是有效的 VLESS 链接")
                return None
            
            # 移除协议前缀
            url_without_scheme = vless_url[8:]
            
            # 分离备注名称
            if '#' in url_without_scheme:
                url_part, name = url_without_scheme.rsplit('#', 1)
                name = urllib.parse.unquote(name)
            else:
                url_part = url_without_scheme
                name = "VLESS节点"
            
            # 分离参数
            if '?' in url_part:
                auth_server_part, params_part = url_part.split('?', 1)
                params = urllib.parse.parse_qs(params_part)
            else:
                auth_server_part = url_part
                params = {}
            
            # 解析用户ID和服务器信息
            if '@' not in auth_server_part:
                logger.error("VLESS 链接格式错误：缺少 @")
                return None
            
            uuid, server_port = auth_server_part.split('@', 1)
            
            # 解析服务器和端口
            if ':' in server_port:
                server, port = server_port.rsplit(':', 1)
                try:
                    port = int(port)
                except ValueError:
                    logger.error(f"端口格式错误: {port}")
                    return None
            else:
                server = server_port
                port = 443  # 默认端口
            
            # 处理 IPv6 地址
            if server.startswith('[') and server.endswith(']'):
                server = server[1:-1]
            
            # 解析参数
            parsed_params = self._parse_params(params)
            
            # 构建代理配置
            proxy_config = {
                'name': name,
                'type': 'vless',
                'server': server,
                'port': port,
                'uuid': uuid,
                'cipher': 'none',  # VLESS 默认不加密
                **parsed_params
            }
            
            # 验证配置
            if self._validate_config(proxy_config):
                return proxy_config
            else:
                logger.error("VLESS 配置验证失败")
                return None
                
        except Exception as e:
            logger.error(f"解析 VLESS 链接失败: {e}")
            return None
    
    def _parse_params(self, params):
        """解析 VLESS 参数"""
        config = {}
        
        # 传输协议
        network = self._get_param(params, 'type', 'tcp')
        if network in self.supported_networks:
            config['network'] = network
        else:
            logger.warning(f"不支持的传输协议: {network}，使用默认 tcp")
            config['network'] = 'tcp'
        
        # 安全传输
        security = self._get_param(params, 'security', 'none')
        if security in self.supported_securities:
            config['tls'] = security in ['tls', 'reality']
            if security == 'reality':
                config['reality-opts'] = self._parse_reality_params(params)
        else:
            logger.warning(f"不支持的安全传输: {security}")
            config['tls'] = False
        
        # TLS 相关参数
        if config.get('tls'):
            sni = self._get_param(params, 'sni')
            if sni:
                config['servername'] = sni
            
            alpn = self._get_param(params, 'alpn')
            if alpn:
                config['alpn'] = alpn.split(',')
            
            # 跳过证书验证
            if self._get_param(params, 'allowInsecure') == '1':
                config['skip-cert-verify'] = True
        
        # WebSocket 相关参数
        if network == 'ws':
            ws_opts = {}
            
            path = self._get_param(params, 'path', '/')
            ws_opts['path'] = path
            
            host = self._get_param(params, 'host')
            if host:
                ws_opts['headers'] = {'Host': host}
            
            config['ws-opts'] = ws_opts
        
        # gRPC 相关参数
        elif network == 'grpc':
            grpc_opts = {}
            
            service_name = self._get_param(params, 'serviceName', 'GunService')
            grpc_opts['grpc-service-name'] = service_name
            
            config['grpc-opts'] = grpc_opts
        
        # HTTP/2 相关参数
        elif network == 'h2':
            h2_opts = {}
            
            path = self._get_param(params, 'path', '/')
            h2_opts['path'] = path
            
            host = self._get_param(params, 'host')
            if host:
                h2_opts['host'] = host.split(',')
            
            config['h2-opts'] = h2_opts
        
        # 流控制
        flow = self._get_param(params, 'flow')
        if flow:
            config['flow'] = flow
        
        return config
    
    def _parse_reality_params(self, params):
        """解析 Reality 参数"""
        reality_opts = {}
        
        public_key = self._get_param(params, 'pbk')
        if public_key:
            reality_opts['public-key'] = public_key
        
        short_id = self._get_param(params, 'sid')
        if short_id:
            reality_opts['short-id'] = short_id
        
        return reality_opts
    
    def _get_param(self, params, key, default=None):
        """获取参数值"""
        if key in params and params[key]:
            return params[key][0]
        return default
    
    def _validate_config(self, config):
        """验证代理配置"""
        required_fields = ['name', 'type', 'server', 'port', 'uuid']
        
        for field in required_fields:
            if field not in config or not config[field]:
                logger.error(f"缺少必需字段: {field}")
                return False
        
        # 验证端口范围
        if not (1 <= config['port'] <= 65535):
            logger.error(f"端口超出范围: {config['port']}")
            return False
        
        # 验证 UUID 格式
        uuid = config['uuid']
        if len(uuid) != 36 or uuid.count('-') != 4:
            logger.warning(f"UUID 格式可能不正确: {uuid}")
        
        return True

    def parse_subscription(self, content):
        """解析订阅内容，支持多个 VLESS 链接"""
        proxies = []
        
        # 尝试 base64 解码
        try:
            decoded_content = base64.b64decode(content).decode('utf-8')
        except:
            decoded_content = content
        
        # 按行分割并解析每个链接
        for line in decoded_content.strip().split('\n'):
            line = line.strip()
            if line.startswith('vless://'):
                proxy = self.parse(line)
                if proxy:
                    proxies.append(proxy)
        
        return proxies
